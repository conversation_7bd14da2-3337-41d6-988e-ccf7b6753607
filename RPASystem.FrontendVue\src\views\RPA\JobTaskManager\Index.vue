<template>
    <div class="app-container">
      <!-- 顶部操作栏改为与菜单页面类似的搜索表单 -->
      <el-form :model="searchForm" :inline="true" v-show="showSearch">
        <el-form-item label="任务名称">
          <el-input v-model="searchForm.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleSearch" style="width: 200px;"/>
        </el-form-item>
        <el-form-item label="程序名称">
          <el-input v-model="searchForm.programName" placeholder="请输入程序名称" clearable @keyup.enter="handleSearch" style="width: 200px;"/>
        </el-form-item>
        <el-form-item label="资源机">
          <el-input v-model="searchForm.resourceMachine" placeholder="请输入资源机" clearable @keyup.enter="handleSearch" style="width: 200px;"/>
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" clearable style="width: 150px;">
            <el-option label="普通任务" :value="0" />
            <el-option label="编排任务" :value="1" />
            <el-option label="系统编排拆分任务" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px;">
            <el-option label="待运行" value="Pending" />
            <el-option label="运行中" value="Running" />
            <el-option label="成功" value="Success" />
            <el-option label="失败" value="Failed" />
            <el-option label="已取消" value="Cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 修改功能按钮栏 -->
      <!-- 功能按钮栏，参考菜单页面样式 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5" style="margin-left: auto;">
          <el-button type="primary" plain @click="showAddDialog">
            <el-icon><Plus /></el-icon>新增任务
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain @click="toggleExpandAll">
            <el-icon><Sort /></el-icon>展开/折叠
          </el-button>
        </el-col>
      </el-row>

      <!-- 修改表格列结构，隐藏ID列，使任务名称列成为树节点 -->
      <el-table
        :data="treeData"
        row-key="jobTaskId"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        style="width: 100%; margin-top: 8px;"
        :row-class-name="tableRowClassName"
        border
        size="small"
        :header-cell-style="{background:'#f8f8f9',color:'#606266'}"
        ref="taskTableRef"
        :default-expand-all="isExpandAll"
      >
        <!-- 将任务名称列改为树节点列 -->
        <el-table-column prop="jobTaskName" label="任务名称" min-width="180" class-name="tree-node-column">
          <template #default="scope">
            <span :class="{ 'child-task': scope.row.parentTaskID !== 0 }">{{ scope.row.jobTaskName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="exeProgramName" label="程序名称" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span 
              v-if="scope.row.taskType === 2" 
              class="clickable-text"
              @click="showSubTasks(scope.row)"
            >
              {{ scope.row.exeProgramName }}
            </span>
            <span v-else>{{ scope.row.exeProgramName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taskType" label="任务类型" width="90" show-overflow-tooltip>
          <template #default="scope">
            <el-tag 
              :type="getTaskTypeStyle(scope.row.taskType)"
              effect="plain"
            >
              {{ 
                scope.row.taskType === 0 ? '普通任务' : 
                scope.row.taskType === 1 ? '编排任务' : 
                scope.row.taskType === 2 ? '拆分任务' : 
                '未知类型'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="60"  />
        <el-table-column prop="createdAt" label="创建时间" width="95" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="95" show-overflow-tooltip />
        <el-table-column label="运行时长" width="95" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="scope.row.endTime"
              :content="`结束时间: ${scope.row.endTime}`"
              placement="top"
              effect="light"
            >
              <span>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
            </el-tooltip>
            <span v-else>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="resourceSelection" label="资源选择" width="70" show-overflow-tooltip />
        <el-table-column prop="assignedResourceMachine" label="资源机" width="70" show-overflow-tooltip>
          <template #default="scope">
            <span 
              v-if="scope.row.status === 'Running' && scope.row.assignedResourceMachine" 
              class="clickable-text"
              @click="handleRemoteDesktop(scope.row.assignedResourceMachine)"
            >
              {{ scope.row.assignedResourceMachine }}
            </span>
            <span v-else>{{ scope.row.assignedResourceMachine }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="95">
          <template #default="scope">
            <!-- 普通任务显示普通状态标签 -->
            <template v-if="scope.row.taskType === 0">
              <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
            
            <!-- 系统编排拆分任务显示进度条 -->
            <template v-else-if="scope.row.taskType === 2">
              <el-tooltip placement="top" :show-after="100">
                <template #content>
                  <div style="text-align: left; white-space: pre-wrap;">
                    总状态：{{ scope.row.status }}<br>
                    总任务数：{{ scope.row.childTaskStats?.total || 0 }}<br>
                    成功：{{ scope.row.childTaskStats?.success || 0 }}<br>
                    运行中：{{ scope.row.childTaskStats?.running || 0 }}<br>
                    失败：{{ scope.row.childTaskStats?.failed || 0 }}<br>
                    等待中：{{ scope.row.childTaskStats?.pending || 0 }}<br>
                    已取消：{{ scope.row.childTaskStats?.cancelled || 0 }}
                  </div>
                </template>
                <div class="progress-container">
                  <div class="progress-bar">
                    <div class="progress-segment success" :style="{ width: getProgressWidth(scope.row, 'Success') }"></div>
                    <div class="progress-segment running" :style="{ width: getProgressWidth(scope.row, 'Running') }"></div>
                    <div class="progress-segment failed" :style="{ width: getProgressWidth(scope.row, 'Failed') }"></div>
                    <div class="progress-segment pending" :style="{ width: getProgressWidth(scope.row, 'Pending') }"></div>
                    <div class="progress-segment cancelled" :style="{ width: getProgressWidth(scope.row, 'Cancelled') }"></div>
                  </div>
                </div>
              </el-tooltip>
            </template>

            <!-- 编排任务显示普通状态标签 -->
            <template v-else>
              <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="inputParameters" label="输入参数" width="80" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="hasInputFile(scope.row.inputParameters)"
              :content="scope.row.inputParameters"
              placement="top"
              effect="light"
            >
              <span 
                class="clickable-text"
                @click="handleInputFileDownload(scope.row.inputParameters)"
              >
                下载输入
              </span>
            </el-tooltip>
            <span v-else>{{ scope.row.inputParameters }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="outputResults" label="返回结果" width="80" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="hasReturnResult(scope.row.outputResults)"
              :content="scope.row.outputResults"
              placement="top"
              effect="light"
            >
              <span 
                class="clickable-text"
                @click="copyReturnResult(scope.row.outputResults)"
              >
                复制结果
              </span>
            </el-tooltip>
            <span v-else>{{ scope.row.outputResults }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="outputFile" label="输出文件" min-width="80" show-overflow-tooltip>
          <template #default="scope">
            <span 
              v-if="scope.row.outputFile && isNetworkSharePath(scope.row.outputFile)"
              class="clickable-text"
              @click="openDirectory(scope.row.outputFile)"
              :title="scope.row.outputFile"
            >
              打开目录
            </span>
            <span v-else-if="scope.row.outputFile" :title="scope.row.outputFile">
              {{ scope.row.outputFile }}
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试数" width="55" align="center" />
        <el-table-column prop="notes" label="备注" min-width="80" show-overflow-tooltip />
        <!-- 移除操作列的fixed="right"属性 -->
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button-group>
              <!-- 重试按钮 -->
              <el-button
                v-if="scope.row.status !== 'Pending' && scope.row.status !== 'Running' && scope.row.taskType !== 1"
                type="primary"
                :icon="RefreshRight"
                circle
                size="small"
                @click="retryJobTask(scope.row.jobTaskId)"
              />

              <!-- 停止按钮 -->
              <el-button 
                v-if="scope.row.status === 'Running' || scope.row.status === 'Pending'" 
                type="warning" 
                :icon="VideoPause" 
                circle 
                size="small" 
                @click="stopJobTask(scope.row.jobTaskId)"
              />

              <!-- 编辑按钮 -->
              <el-button 
                type="primary" 
                :icon="Edit" 
                circle 
                size="small" 
                @click="showEditDialog(scope.row)" 
              />

              <!-- 删除按钮 -->
              <el-button 
                type="danger" 
                :icon="Delete" 
                circle 
                size="small" 
                @click="handleDelete(scope.row)" 
              />
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器样式优化 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNumber"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :background="true"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 保持原有对话框部分代码不变 -->
      <el-dialog v-model="dialogVisible" title="新增任务">
        <el-form :model="newJobTask" label-width="120px">
          <el-form-item label="程序列表">
            <el-select 
              v-model="newJobTask.exeProgramId" 
              placeholder="请选择程序" 
              @change="handleProgramChange" 
              filterable
            >
              <el-option 
                v-for="program in exePrograms" 
                :key="program.id" 
                :label="program.programName" 
                :value="program.id" 
              />
            </el-select>
          </el-form-item>

          <el-form-item label="优先级">
            <el-input-number v-model="newJobTask.priority" :min="1" :max="100" :step="1" />
          </el-form-item>

          <el-form-item label="程序参数">
            <job-task-parameters-editor
              :program-input-parameters="selectedProgramInputParameters"
              :initial-parameters="newJobTask.inputParameters"
              :task-type="newJobTask.taskType"
              @update:parameters="updateInputParameters"
              @file-type-error="handleFileTypeError"
            />
          </el-form-item>

          <!-- 只有当选择RPA类型程序时才显示任务类型 -->
          <template v-if="isRPAProgram">
            <el-form-item label="任务类型">
              <el-radio-group v-model="newJobTask.taskType">
                <el-radio-button :label="0">普通任务</el-radio-button>
                <el-radio-button :label="2">系统编排拆分任务</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <!-- 系统编排拆分任务的额外参数 -->
            <template v-if="newJobTask.taskType === 2">
              <el-form-item label="每份拆分数量">
                <el-input-number 
                  v-model="splitTaskConfig.excelPerSplitNum" 
                  :min="1" 
                  :step="1" 
                  placeholder="请输入拆分数量"
                />
              </el-form-item>
              
              <el-form-item label="合并类型">
                <el-select v-model="splitTaskConfig.mergeType" placeholder="请选择合并类型" clearable>
                  <el-option label="不覆盖文件" value="0" />
                  <el-option label="覆盖文件" value="1" />
                  <el-option label="不覆盖文件合并相同Excel" value="2" />
                  <el-option label="覆盖文件合并相同Excel" value="3" />
                </el-select>
              </el-form-item>
            </template>
          </template>

          <el-form-item label="资源选择" v-if="showResourceSelection">
            <resource-selector 
              :model-value="newJobTask.resourceSelection"
              @update:model-value="val => newJobTask.resourceSelection = val"
            />
          </el-form-item>

          <!-- 添加输出文件服务器选择 -->
          <el-form-item label="输出文件服务器" v-if="showResourceSelection">
            <el-select 
              v-model="newJobTask.outputFile" 
              placeholder="请选择输出文件服务器"
              clearable
            >
              <el-option 
                v-for="server in fileServers" 
                :key="server"
                :label="server" 
                :value="server"
              />
            </el-select>
          </el-form-item>

          <!-- 在资源选择后面添加备注字段 -->
          <el-form-item label="备注">
            <el-input 
              v-model="newJobTask.notes" 
              placeholder="请输入备注信息"
            />
          </el-form-item>

        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" :disabled="isSaving">取消</el-button>
            <el-button 
              type="primary" 
              @click="createJobTask" 
              :loading="isSaving"
              :disabled="isSaving"
            >
              {{ isSaving ? '保存中...' : '保存' }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加全屏加载 -->
      <el-loading 
        v-model:visible="fullscreenLoading" 
        fullscreen 
        element-loading-text="正在创建任务..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
      />

      <!-- 添加编辑对话框 -->
      <el-dialog v-model="editDialogVisible" title="编辑任务">
        <el-form :model="editJobTask" label-width="120px">
          <el-form-item label="优先级">
            <el-input-number 
              v-model="editJobTask.priority" 
              :min="1" 
              :max="100" 
              :step="1"
              placeholder="不修改留空"
              clearable
            />
          </el-form-item>

          <el-form-item label="资源选择">
            <resource-selector 
              :model-value="editJobTask.resourceSelection"
              @update:model-value="val => editJobTask.resourceSelection = val"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="editJobTask.status" placeholder="不修改请留空" clearable>
              <el-option label="待运行" :value="0" />
              <el-option label="运行中" :value="1" />
              <el-option label="成功" :value="2" />
              <el-option label="失败" :value="3" />
              <el-option label="已取消" :value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="editJobTask.notes" placeholder="不修改请留空" clearable />
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleEditSubmit" :loading="isSaving">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加子任务弹窗组件 -->
      <sub-task-dialog
        v-if="subTaskDialog.visible"
        v-model:visible="subTaskDialog.visible"
        :parent-task-id="subTaskDialog.parentTaskId"
        :parent-task-name="subTaskDialog.parentTaskName"
      />

      <!-- 远程桌面组件 -->
      <remote-desktop
        v-if="remoteDesktop.visible"
        v-model:visible="remoteDesktop.visible"
        :machine-name="remoteDesktop.machineName"
      />
    </div>
</template>

<script setup name="jobtaskmanager">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RefreshRight, VideoPause, Delete, Edit, Plus, Sort } from '@element-plus/icons-vue'
import * as signalR from '@microsoft/signalr'
import { copyToClip } from '@/utils/clipboard'
import ResourceSelector from '@/views/components/RPA/ResourceSelector.vue'
import JobTaskParametersEditor from './ParametersEditor.vue'
import SubTaskDialog from './SubTaskDialog.vue'
import RemoteDesktop from '@/views/components/RPA/RemoteDesktop.vue'
import { 
  searchJobTasks, 
  getAllExePrograms, 
  getExeProgramDetail, 
  createOrchestrationTask,
  deleteJobTask, 
  retryJobTask as apiRetryJobTask, 
  stopJobTask as apiStopJobTask, 
  updateJobTask,
  downloadFile as apiDownloadFile
} from '@/api/RPA/JobTaskManager'
import { getConfigValue } from '@/api/RPA/SystemConfigManager'

// 添加控制搜索表单显示的变量
const showSearch = ref(true)

// 添加展开/折叠相关变量
const taskTableRef = ref(null)
const isExpandAll = ref(false)

// 添加展开/折叠控制方法
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
  
  // 需要等待DOM更新后再操作表格
  nextTick(() => {
    if (taskTableRef.value) {
      const expandRows = (data) => {
        data.forEach(row => {
          taskTableRef.value.toggleRowExpansion(row, isExpandAll.value)
          if (row.children && row.children.length > 0) {
            expandRows(row.children)
          }
        })
      }
      
      expandRows(treeData.value)
    }
  })
}

// 数据定义
const jobTasks = ref([])
const exePrograms = ref([])
const dialogVisible = ref(false)
const newJobTask = ref({
  priority: 10,
  exeProgramId: null,
  inputParameters: '',
  taskType: 0,
  resourceSelection: '',
  notes: '',
  outputFile: ''
})
const fileServers = ref([])

// 子任务对话框
const subTaskDialog = ref({
  visible: false,
  parentTaskId: null,
  parentTaskName: ''
})

// 远程桌面状态
const remoteDesktop = ref({
  visible: false,
  machineName: ''
})

// 编辑对话框
const editDialogVisible = ref(false)
const editJobTask = ref({
  id: null,
  priority: null,
  resourceSelection: '',
  status: '',
  notes: ''
})

// 定时器引用
let timer = null

// 修改树形结构转换逻辑
const treeData = computed(() => {
  const tree = []
  const map = {}

  jobTasks.value.forEach(task => {
    // 创建对象，避免直接修改原始数据
    const newTask = { ...task, children: [] }
    map[task.jobTaskId] = newTask
    
    if (task.parentTaskID === 0) {
      tree.push(newTask)
    } else {
      if (!map[task.parentTaskID]) {
        // 如果父任务不存在，将其作顶级任务
        tree.push(newTask)
      } else {
        map[task.parentTaskID].children.push(newTask)
      }
    }
  })

  return tree
})

const searchForm = ref({
  taskName: '',
  programName: '',
  resourceMachine: '',
  taskType: null,
  status: ''
})

const pagination = ref({
  pageNumber: 1,
  pageSize: 10,
  total: 0
})

// 修改获取任务列表的方法
const fetchJobTasks = async () => {
  try {
    const params = {
      ...searchForm.value,
      pageNumber: pagination.value.pageNumber,
      pageSize: pagination.value.pageSize
    }
    
    const response = await searchJobTasks(params)
    jobTasks.value = response.data.items
    pagination.value.total = response.data.totalCount
    
    // 数据加载完成后，初始化表格展开状态
    if (isExpandAll.value) {
      nextTick(() => {
        initTableExpand()
      })
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  }
}

// 添加初始化表格展开的方法
const initTableExpand = () => {
  if (!taskTableRef.value) return
  
  // 等待表格渲染完成
  setTimeout(() => {
    const expandRows = (data) => {
      data.forEach(row => {
        taskTableRef.value.toggleRowExpansion(row, true)
        if (row.children && row.children.length > 0) {
          expandRows(row.children)
        }
      })
    }
    
    expandRows(treeData.value)
  }, 100)
}

const handleSearch = () => {
  pagination.value.pageNumber = 1
  fetchJobTasks()
}

const resetSearch = () => {
  searchForm.value = {
    taskName: '',
    programName: '',
    resourceMachine: '',
    taskType: null,
    status: ''
  }
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.value.pageNumber = page
  fetchJobTasks()
}

const fetchExePrograms = async () => {
  try {
    const response = await getAllExePrograms()
    exePrograms.value = response.data
  } catch (error) {
    console.error('获取程序列表失败:', error)
  }
}

// 重置表单方法
const resetForm = () => {
  newJobTask.value = {
    priority: 10,
    exeProgramId: '',
    inputParameters: '{}',
    taskType: 0,
    resourceSelection: '',
    notes: '',
    outputFile: fileServers.value.length > 0 ? fileServers.value[0] : ''
  }
  splitTaskConfig.value = {
    excelPerSplitNum: 10,
    mergeType: ''
  }
  selectedProgramInputParameters.value = '[]'
}

const showAddDialog = () => {
  resetForm()
  dialogVisible.value = true
}

const isSaving = ref(false)
const fullscreenLoading = ref(false)

const createJobTask = async () => {
  if (isSaving.value) return

  try {
    isSaving.value = true
    fullscreenLoading.value = true

    // 如果是系统编排拆分任务，需要合并splitTaskConfig到输入参数中
    if (newJobTask.value.taskType === 2) {
      const currentParams = JSON.parse(newJobTask.value.inputParameters || '{}')
      const mergedParams = {
        ...currentParams,
        ExcelPerSplitNum: splitTaskConfig.value.excelPerSplitNum.toString(),
      }
      
      // 只有当选择了合并类型时才添加 MergeType 字段
      if (splitTaskConfig.value.mergeType !== '') {
        mergedParams.MergeType = splitTaskConfig.value.mergeType
      }
      
      newJobTask.value.inputParameters = JSON.stringify(mergedParams)
    }

    // 验证参数
    if (!validateSplitTaskParameters(newJobTask.value.inputParameters)) {
      isSaving.value = false
      fullscreenLoading.value = false
      return
    }

    // 构造OrchestrationTaskDto
    const orchestrationTask = {
      programName: exePrograms.value.find(p => p.id === newJobTask.value.exeProgramId)?.programName,
      inputParameters: newJobTask.value.inputParameters,
      taskPriority: newJobTask.value.priority,
      taskType: newJobTask.value.taskType,
      resourceSelection: newJobTask.value.resourceSelection,
      notes: newJobTask.value.notes,
      outputFile: newJobTask.value.outputFile
    }

    // 使用新的接口创建任务
    await createOrchestrationTask(orchestrationTask)
    
    dialogVisible.value = false
    ElMessage.success('创建任务成功')
    resetForm()
    await fetchJobTasks()
  } catch (error) {
    console.error('创建任务失败:', error)
    ElMessage.error('创建任务失败: ' + (error.response?.data?.error || error.message))
  } finally {
    isSaving.value = false
    fullscreenLoading.value = false
  }
}

const handleDelete = (row) => {
  return ElMessageBox.confirm(
    '此操作将永久删除该任务及其所有子任务，是否继续？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteJobTask(row.jobTaskId).then(() => {
        ElMessage.success('删除成功')
        fetchJobTasks()
      })
    })
    .catch(() => {
      // 用户取消时不显示提示
    })
}

const retryJobTask = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要重试该任务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiRetryJobTask(id)
    ElMessage.success('已开始重试任务')
    await fetchJobTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试任务失败:', error)
      ElMessage.error('重试任务失败')
    }
  }
}

const stopJobTask = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确定要停止该任务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiStopJobTask(id)
    ElMessage.success('已发送停止指令')
    await fetchJobTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止任务失败:', error)
      ElMessage.error('停止任务失败')
    }
  }
}

const selectedProgramInputParameters = ref('[]')

const handleProgramChange = async (programId) => {
  // 获取选中的程序
  const selectedProgram = exePrograms.value.find(p => p.id === programId)
  if (!selectedProgram) return

  // 设置程序类型
  isRPAProgram.value = selectedProgram.programType === 0
  
  try {
    const response = await getExeProgramDetail(programId)
    selectedProgramInputParameters.value = response.data.inputParameters
    
    // 自动填充资源选择 - 使用nextTick确保DOM更新
    await nextTick()
    if (selectedProgram.resourceSelection) {
      // 使用新的引用更新对象
      newJobTask.value = {
        ...newJobTask.value,
        resourceSelection: selectedProgram.resourceSelection
      }
    } else {
      newJobTask.value = {
        ...newJobTask.value,
        resourceSelection: ''
      }
    }
  } catch (error) {
    console.error('获取程序参数失败:', error)
    ElMessage.error('获取程序参数失败')
  }
}

const updateInputParameters = (newParameters) => {
  newJobTask.value.inputParameters = newParameters
}

const validateSplitTaskParameters = (parameters) => {
  if (newJobTask.value.taskType === 2) {
    try {
      const params = JSON.parse(parameters)
      if (!params.InputFile || params.InputFile === '') {
        ElMessage.error('系统编排拆分任务必须上传InputFile文件')
        return false
      }
    } catch (error) {
      ElMessage.error('参数格式错误')
      return false
    }
  }
  return true
}

const handleFileTypeError = (message) => {
  ElMessage.error(message)
}

const splitTaskConfig = ref({
  excelPerSplitNum: 10,
  mergeType: ''
})

// 修改 isRPAProgram 的判断逻辑
const isRPAProgram = ref(false)

// 修改 tableRowClassName 方法
const tableRowClassName = ({ row }) => {
  // 只保留子任务样式
  return row.parentTaskID !== 0 ? 'child-task-row' : ''
}

// 添加状态标签样式方法
const getStatusType = (status) => {
  const statusMap = {
    'Pending': 'info',
    'Running': 'primary',
    'Success': 'success',
    'Failed': 'danger',
    'Cancelled': 'warning'
  }
  return statusMap[status] || 'info'
}

// 检查是否有InputFile
const hasInputFile = (inputParameters) => {
  try {
    const params = JSON.parse(inputParameters || '{}')
    return params.InputFile && params.InputFile !== ''
  } catch {
    return false
  }
}

// 处理输入文件下载
const handleInputFileDownload = async (inputParameters) => {
  try {
    const params = JSON.parse(inputParameters || '{}')
    if (params.InputFile) {
      const fileId = params.InputFile
      
      // 下载文件
      const response = await apiDownloadFile(fileId)
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      
      // 生成日期时间格式的文件名
      const now = new Date()
      const fileName = now.getFullYear() +
        String(now.getMonth() + 1).padStart(2, '0') +
        String(now.getDate()).padStart(2, '0') +
        String(now.getHours()).padStart(2, '0') +
        String(now.getMinutes()).padStart(2, '0') +
        String(now.getSeconds()).padStart(2, '0') +
        '.xlsx'
      
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败，请重试')
  }
}

// 检查是否有ReturnResult
const hasReturnResult = (outputResults) => {
  try {
    const result = JSON.parse(outputResults || '{}')
    return result.ReturnResult !== undefined
  } catch {
    return false
  }
}

// 修改 copyReturnResult 方法
const copyReturnResult = (outputResults) => {
  try {
    const result = JSON.parse(outputResults || '{}')
    if (result.ReturnResult !== undefined) {
      copyToClip(result.ReturnResult)
    }
  } catch (error) {
    console.error('解析输出结果失败:', error)
    ElMessage.error('解析输出结果失败')
  }
}

// 添加处理页面大小变化的方法
const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.pageNumber = 1 // 切换页面大小时重置为第一页
  fetchJobTasks()
}

let connection = null

// 添加 SignalR 连接初始化
const initSignalRConnection = async () => {
  try {
    connection = new signalR.HubConnectionBuilder()
      .withUrl('/resourceMachineHub')
      .build()

    connection.on('RefreshJobTasks', () => {
      console.log('RefreshJobTasks signal received')
      fetchJobTasks()
    })

    await connection.start()
    console.log('SignalR Connected')
  } catch (err) {
    console.error('SignalR Connection Error: ', err)
    ElMessage.error('实时连接失败，页面更新可能不及时')
  }
}

// 获取文件服务器列表
const fetchFileServers = async () => {
  try {
    const response = await getConfigValue('FileStorage.ServerIPs')
    if (response.data) {
      fileServers.value = response.data.split(',').map(server => server.trim()).filter(server => server !== '')
      // 如果有服务器，默认选择第一个
      if (fileServers.value.length > 0) {
        newJobTask.value.outputFile = fileServers.value[0]
      }
    }
  } catch (error) {
    console.error('获取文件服务器列表失败:', error)
  }
}

// 添加计算属性来控制资源选择的显示
const showResourceSelection = computed(() => {
  if (!newJobTask.value.exeProgramId) return false
  const selectedProgram = exePrograms.value.find(p => p.id === newJobTask.value.exeProgramId)
  // 如果是编排任务类型（programType === 1），则不显示资源选择
  return selectedProgram?.programType !== 2
})

// 显示子任务对话框
const showSubTasks = (row) => {
  subTaskDialog.value = {
    visible: true,
    parentTaskId: row.jobTaskId,
    parentTaskName: row.jobTaskName
  }
}

// 处理远程桌面
const handleRemoteDesktop = (machineName) => {
  remoteDesktop.value = {
    visible: true,
    machineName
  }
}

// 显示编辑对话框
const showEditDialog = (row) => {
  editJobTask.value = {
    id: row.jobTaskId,
    priority: null,
    resourceSelection: row.resourceSelection || '',
    status: '',
    notes: row.notes || ''
  }
  editDialogVisible.value = true
}

// 处理编辑提交
const handleEditSubmit = async () => {
  try {
    isSaving.value = true
    const updateData = {}
    
    // 只包含已修改的字段
    if (editJobTask.value.priority !== null) {
      updateData.priority = editJobTask.value.priority
    }
    if (editJobTask.value.resourceSelection) {
      updateData.resourceSelection = editJobTask.value.resourceSelection
    }
    if (editJobTask.value.status !== '') {
      updateData.status = editJobTask.value.status
    }
    if (editJobTask.value.notes) {
      updateData.notes = editJobTask.value.notes
    }

    await updateJobTask(editJobTask.value.id, updateData)
    
    ElMessage.success('更新成功')
    editDialogVisible.value = false
    await fetchJobTasks()
  } catch (error) {
    ElMessage.error(`更新失败: ${error.message}`)
  } finally {
    isSaving.value = false
  }
}

// 生命周期钩子
onMounted(async () => {
  // 检查URL中是否有状态参数
  const urlParams = new URLSearchParams(window.location.search)
  const statusParam = urlParams.get('status')
  if (statusParam) {
    searchForm.value.status = statusParam
  }
  
  await fetchJobTasks()
  await fetchExePrograms()
  await fetchFileServers()
  await initSignalRConnection()
})

onUnmounted(() => {
  if (connection) {
    connection.stop()
  }
})

// 计算任务运行时长
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return ''

  const start = new Date(startTime)
  const end = new Date(endTime)
  const diffInSeconds = Math.floor((end - start) / 1000)
  
  if (diffInSeconds < 0) return ''
  
  const days = Math.floor(diffInSeconds / (24 * 3600))
  const hours = Math.floor((diffInSeconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((diffInSeconds % 3600) / 60)
  const seconds = diffInSeconds % 60
  
  if (days === 0 && hours === 0 && minutes === 0) {
    return `${seconds}秒`
  }
  
  if (days === 0 && hours === 0) {
    return minutes === 0 ? `${seconds}秒` : `${minutes}分${seconds}秒`
  }
  
  if (days === 0) {
    return `${hours}时${minutes}分${seconds}秒`
  }
  
  return `${days}天${hours}时${minutes}分${seconds}秒`
}

// 获取子任务总数
const getChildTasksTotal = (row) => {
  return row.children?.length || 0
}

// 获取指定状态的子任务数
const getChildTasksByStatus = (row, status) => {
  return row.children?.filter(task => task.status === status)?.length || 0
}

// 获取其他状态的子任务数
const getOtherStatusCount = (row) => {
  const total = getChildTasksTotal(row)
  const counted = getChildTasksByStatus(row, 'Success') +
    getChildTasksByStatus(row, 'Running') +
    getChildTasksByStatus(row, 'Failed') +
    getChildTasksByStatus(row, 'Pending')
  return total - counted
}

// 获取进度条宽度
const getProgressWidth = (row, status) => {
  if (!row.childTaskStats?.total) return '0%'
  const total = row.childTaskStats.total

  let count
  switch (status) {
    case 'Success':
      count = row.childTaskStats.success
      break
    case 'Running':
      count = row.childTaskStats.running
      break
    case 'Failed':
      count = row.childTaskStats.failed
      break
    case 'Pending':
      count = row.childTaskStats.pending
      break
    case 'Cancelled':
      count = row.childTaskStats.cancelled
      break
    default:
      count = 0
  }

  return `${(count / total * 100).toFixed(1)}%`
}

// 添加任务类型样式方法
const getTaskTypeStyle = (taskType) => {
  const typeMap = {
    0: 'info',    // 普通任务
    1: 'warning', // 编排任务
    2: 'success'  // 系统编排拆分任务
  }
  return typeMap[taskType] || 'info'
}

// 检查是否是网络共享路径
const isNetworkSharePath = (path) => {
  if (!path) return false
  
  // 转换为小写以便不区分大小写比较
  const lowerPath = path.toLowerCase()
  
  // 检查常见的网络共享路径格式:
  // - Windows UNC路径 (\\server\share)
  // - 网络协议 (smb://, ftp://, http://)
  // - 网络路径 (//server/share)
  // - 映射的网络驱动器 (z:\folder 其中z不是c,d,e等本地驱动器)
  
  if (lowerPath.startsWith('\\\\') || lowerPath.startsWith('//')) {
    return true
  }
  
  if (lowerPath.includes('://')) {
    return true
  }
  
  // 检查是否是网络驱动器映射 (假设C-F是本地驱动器)
  const driveLetterMatch = lowerPath.match(/^([a-z]):\\/i)
  if (driveLetterMatch) {
    const driveLetter = driveLetterMatch[1].toLowerCase()
    // 假设g-z是网络驱动器
    if (driveLetter >= 'g' && driveLetter <= 'z') {
      return true
    }
  }
  
  return false
}

// 打开目录处理函数
const openDirectory = (path) => {
  if (!path) return
  
  try {
    // 格式化路径成自定义协议
    const protocolPath = 'opendir:' + path
    console.log('Opening directory:', protocolPath)
    window.location.href = protocolPath
  } catch (error) {
    console.error('打开目录失败:', error)
    ElMessage.error('打开目录失败')
  }
}
</script>

<style scoped>
/* 参考菜单页面的样式 */
.app-container {
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.mb8 {
  margin-bottom: 8px;
}

/* 优化表格样式 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f8f9 !important;
  color: #606266;
  font-weight: 500;
}

.child-task-row {
  background-color: #f8f8f9;
}

.child-task {
  color: #606266;
  margin-left: 8px;
}

/* 自定义树节点列样式，模拟菜单页效果 */
:deep(.tree-node-column .cell) {
  position: relative;
}

/* 修改展开图标的旋转方向 - 默认向下，展开时向上 */
:deep(.el-table__expand-icon) {
  position: relative;
  height: 20px;
  width: 20px;
  cursor: pointer;
  margin-right: 8px;
  transform: rotate(270deg);
}

:deep(.el-table__expand-icon .el-icon) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  font-size: 12px;
  transition: transform 0.2s ease-in-out;
}

:deep(.el-table__expand-icon--expanded .el-icon) {
  transform: translate(-50%, -50%) rotate(180deg);
}

/* 防止文本换行，超出显示省略号 */
:deep(.el-table .cell) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 优化按钮样式 */
.el-button-group .el-button {
  margin-right: 0;
}

/* 优化分页器样式 */
.pagination-container {
  margin-top: 15px;
  padding: 10px 0;
  text-align: right;
}

/* 保留原有的进度条样式 */
.progress-container {
  width: 100%;
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
}

.progress-bar {
  display: flex;
  height: 100%;
  border-radius: 5px;
}

.progress-segment {
  height: 100%;
}

.progress-segment.success {
  background-color: #67c23a;
}

.progress-segment.running {
  background-color: #409eff;
}

.progress-segment.failed {
  background-color: #f56c6c;
}

.progress-segment.pending {
  background-color: #e6a23c;
}

.progress-segment.cancelled {
  background-color: #909399;
}

/* 优化可点击文本样式 */
.clickable-text {
  color: #409EFF;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s;
}

.clickable-text:hover {
  color: #66b1ff;
  text-decoration: underline;
}
</style>